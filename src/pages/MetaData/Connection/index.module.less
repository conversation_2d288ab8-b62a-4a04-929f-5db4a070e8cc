.connection-wrapper {
  background: #ffffff;
  border: 1px solid rgba(212, 214, 217, 0.6);
  border-radius: 6px;
  width: 100%;
  padding: 16px;
  margin: 0 8px 8px 0;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-flow: column nowrap;

  .title {
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 22px;
    line-height: 32px;
    letter-spacing: 0px;
    margin: 8px 0 16px;
  }

  :global {
    .acud-table-cell-ellipsis {
      .toolkit-ellipsis {
        width: 90%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .acud-empty {
      min-height: 50vh;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-flow: column;
    }
  }

  .empty-container {
    flex: 1;
    position: relative;

    .empty-card {
      position: absolute;
      top: 21.5%;
      left: 50%;
      transform: translateX(-50%);
      padding: 12px 12px 20px;
      display: flex;
      flex-flow: column nowrap;
      align-items: center;

      .empty-img {
        width: 700px;
        margin-bottom: 16px;
      }

      .empty-title {
        font-weight: 500;
        line-height: 32px;
        font-size: 24px;
        margin-bottom: 16px;
      }

      .empty-desc {
        font-size: 12px;
        line-height: 20px;
        margin-bottom: 24px;
      }

      button {
        width: 88px;
        font-weight: 500;
      }
    }
  }

  .operation-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .left-btn-container {
      display: flex;
      align-items: center;

      .search-container {
        width: 240px;
      }
    }

    .right-container {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .td-operation-bar {
    display: flex;
    align-items: flex-end;

    & > * {
      margin-right: 12px;
    }

    & > *:last-child {
      margin-right: 0;
    }

    button {
      padding: 0;
      border: none;
      min-width: 0;
      height: auto;
      font-family: PingFang SC;
    }
  }

  .pagination-container {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;

    :global {
      .acud-pagination {
        width: fit-content;
      }
    }
  }
}
