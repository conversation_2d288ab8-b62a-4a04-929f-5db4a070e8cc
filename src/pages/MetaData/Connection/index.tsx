/**
 * @file 数据源管理
 * <AUTHOR>
 */
import {FC, useCallback, useContext, useEffect, useMemo, useState} from 'react';
import useUrlState from '@ahooksjs/use-url-state';
import RefreshButton from '@components/RefreshButton';
import {Button, Pagination, Search, Table, toast, Modal, Loading, Empty, Breadcrumb, Link} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useNavigate} from 'react-router-dom';
import {useSelector} from 'react-redux';
import urls from '@/utils/urls';
import {useRequest} from 'ahooks';
import EditModal from './components/EditModal';
import {
  queryConnectionList,
  IConnection,
  IQueryConnectionListParams,
  deleteConnection
} from '@api/connection';
import IconSvg from '@components/IconSvg';
import {formatTime} from '@utils/moment';
import {ConnectionTypeMap, ConnectionTypeFlatMap} from './constants';
import styles from './index.module.less';
import createEmptyPng from '@assets/png/integration/connection-create.png';
import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {TooltipType} from '@components/AuthComponents/constants';

interface IConnectionProps {}
const Connection: FC<IConnectionProps> = () => {
  const {workspaceId} = useContext(WorkspaceContext);
  const navigate = useNavigate();
  const [{mode}, setUrlParams] = useUrlState();
  const [connectionList, setConnectionList] = useState<Array<IConnection>>([]); // 数据源列表
  const workspacePermission = useSelector((state: IAppState) => state.globalAuthSlice.workspacePermission);
  const [isModalVisible, setIsModalVisible] = useState(false); // 编辑弹窗是否可见
  const [keyword, setKeyword] = useState(''); // 搜索关键字
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 20,
    total: 0
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]); // 选中的行
  const [connection, setConnection] = useState<IConnection>(); // 当前编辑的数据源
  const [typeFilterValue, setTypeFilterValue] = useState([]); // 类型筛选值
  const [createTimeSortValue, setCreateTimeSortValue] = useState<boolean>(); // 创建时间排序值

  // 如果是创建模式，则打开弹窗
  useEffect(() => {
    if (mode === 'create') {
      setTimeout(() => {
        setIsModalVisible(true);
      }, 1000);
      setUrlParams((pre) => ({
        ...pre,
        mode: undefined
      }));
    }
  }, []);

  const {run: runConnectionList, loading} = useRequest(queryConnectionList, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        setConnectionList(res?.result?.connections || []);
        setPagination((prev) => ({...prev, total: res?.result?.total || 0}));
        setSelectedRowKeys([]);
      }
    }
  });

  // 获取数据源列表
  const getConnectionList = useCallback(
    (params: IQueryConnectionListParams = {}) => {
      if (params.pageNo) {
        setPagination((prev) => ({...prev, pageNo: params.pageNo!}));
      }
      runConnectionList(workspaceId, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize,
        filter: keyword,
        type: typeFilterValue?.join(','),
        ...(createTimeSortValue != null
          ? {
              asc: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {}),
        ...params
      });
    },
    [
      runConnectionList,
      workspaceId,
      pagination.pageNo,
      pagination.pageSize,
      keyword,
      typeFilterValue,
      createTimeSortValue
    ]
  );

  // 初始化查询
  useEffect(() => {
    getConnectionList();
  }, []);

  // 已选中的行计数
  const showSelectedTotal = useCallback(() => {
    return `已选${selectedRowKeys.length}/共${pagination.total}条`;
  }, [pagination.total, selectedRowKeys]);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 监听点击刷新按钮
  const onClickRefreshBtn = useCallback(() => {
    getConnectionList();
  }, [getConnectionList]);

  // 搜索
  const onConfirmSearch = useCallback(
    (value: string) => {
      setKeyword(value);
      getConnectionList({
        filter: value,
        pageNo: 1
      });
    },
    [getConnectionList]
  );

  // 点击新建按钮
  const onClickAddBtn = () => {
    setIsModalVisible(true);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setIsModalVisible(false);
    setConnection(undefined);
  };

  // 删除
  const handleDelete = useCallback(
    (connection?: IConnection) => {
      let names = selectedRowKeys;
      if (connection) {
        names = [connection.name];
      }

      Modal.confirm({
        title: '删除数据源',
        content: `删除后⽆法恢复！请确定是否要删除${connection ? '“' + connection.name + '“' : '所选择的内容'}`,
        okText: '删除',
        onOk() {
          return deleteConnection(workspaceId, names).then((res) => {
            if (res.success) {
              toast.success({message: '删除成功', duration: 5});
              getConnectionList();
            }
          });
        },
        onCancel() {}
      });
    },
    [selectedRowKeys, workspaceId, getConnectionList]
  );

  // 监听表格发生变化
  const onTableChange = useCallback(
    (...args: any) => {
      // 筛选
      const type = args?.[1]?.type || null;
      setTypeFilterValue(type);

      let createTimeSortValue = args?.[2]?.order;
      createTimeSortValue =
        createTimeSortValue === 'ascend' ? true : createTimeSortValue === 'descend' ? false : null;
      setCreateTimeSortValue(createTimeSortValue);

      getConnectionList({
        pageNo: 1,
        type: type?.join(','),
        ...(createTimeSortValue != null
          ? {
              asc: createTimeSortValue,
              orderBy: 'createdAt'
            }
          : {asc: undefined, orderBy: undefined})
      });
    },
    [getConnectionList]
  );

  // 跳转详情页
  const jumpDetail = useCallback((name) => {
    navigate(`${urls.connectionDetail}?name=${name}`);
  }, []);

  // 批量删除按钮是否可用
  const canBatchDelete = useMemo(() => {
    const selectedList = connectionList.filter((item) => selectedRowKeys.includes(item.name));
    const hasManagePrivilege = selectedList.every((item) => item.privileges?.includes(Privilege.Manage)); // 勾选的数据源是否都有管理权限
    return selectedRowKeys.length > 0 && hasManagePrivilege;
  }, [selectedRowKeys, connectionList]);

  const columns = useMemo(() => {
    const col: ColumnsType<IConnection> = [
      {
        title: '名称',
        dataIndex: 'name',
        width: '26%',
        key: 'name',
        ellipsis: {showTitle: false},
        render: (name) => {
          return (
            <Ellipsis tooltip={name}>
              <Link onClick={() => jumpDetail(name)}>{name}</Link>
            </Ellipsis>
          );
        }
      },
      {
        title: '数据源类型',
        dataIndex: 'type',
        width: 200,
        filterMultiple: true,
        filters: ConnectionTypeMap.flatMap((group) =>
          group.typeList.map((item) => ({
            value: item.type,
            text: item.type
          }))
        ),
        key: 'type',
        render: (type) => {
          const target = ConnectionTypeFlatMap[type];
          return (
            <div className="flex items-center">
              <IconSvg
                type={target?.icon}
                color={target?.color || undefined}
                size={14}
                className="bordered-circle-icon"
              />
              <span style={{marginLeft: 8}}>{type}</span>
            </div>
          );
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 280,
        sorter: true,
        render: (time) => formatTime(time)
      },
      {
        title: '描述',
        dataIndex: 'comment',
        width: '20%',
        ellipsis: {showTitle: false},
        render: (comment) => {
          return <Ellipsis tooltip={comment}>{comment}</Ellipsis>;
        }
      },
      {
        title: '操作',
        width: 180,
        render: (record: IConnection) => {
          return (
            <div className={styles['td-operation-bar']}>
              <AuthButton
                isAuth={record.privileges?.includes(Privilege.Manage)}
                type="actiontext"
                onClick={() => {
                  setIsModalVisible(true);
                  setConnection(record);
                }}
              >
                编辑
              </AuthButton>
              <AuthButton
                isAuth={record.privileges?.includes(Privilege.Manage)}
                type="actiontext"
                onClick={() => {
                  handleDelete(record);
                }}
              >
                删除
              </AuthButton>
            </div>
          );
        }
      }
    ];
    return col;
  }, [jumpDetail]);

  return (
    <div className={styles['connection-wrapper']}>
      <Loading loading={loading} />
      <Breadcrumb>
        <Breadcrumb.Item>目录列表</Breadcrumb.Item>
        <Breadcrumb.Item>数据源</Breadcrumb.Item>
      </Breadcrumb>
      <div className={styles['title']}>数据源</div>
      {!pagination.total && !keyword ? (
        <div className={styles['empty-container']}>
          <div className={styles['empty-card']}>
            <img src={createEmptyPng} alt="" className={styles['empty-img']}></img>
            <div className={styles['empty-title']}>创建数据源</div>
            <div className={styles['empty-desc']}>适用于连接多种外部数据源</div>
            <AuthButton
              type="primary"
              isAuth={workspacePermission[Privilege.CreateConnection]}
              onClick={onClickAddBtn}
              icon={<IconSvg type="add" />}
              tooltipType={TooltipType.Function}
              className={styles['empty-btn']}
            >
              立即创建
            </AuthButton>
          </div>
        </div>
      ) : (
        <>
          <div className={styles['operation-container']}>
            <div className={styles['left-btn-container']}>
              <Search
                placeholder="请输入源名称、描述进行搜索"
                className={styles['search-container']}
                allowClear
                onSearch={onConfirmSearch}
                width={300}
              />
            </div>
            <div className={styles['right-container']}>
              <div>{showSelectedTotal()}</div>
              <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
              <Button
                onClick={() => handleDelete()}
                disabled={!canBatchDelete}
                style={{minWidth: 48, padding: 0}}
              >
                删除
              </Button>
              <AuthButton
                type="primary"
                isAuth={workspacePermission[Privilege.CreateConnection]}
                onClick={onClickAddBtn}
                icon={<IconSvg type="add" />}
                tooltipType={TooltipType.Function}
              >
                创建数据源
              </AuthButton>
            </div>
          </div>

          <Table
            dataSource={connectionList}
            columns={columns}
            rowKey="name"
            scroll={pagination.total > 0 ? {y: 'calc(100vh - 306px)'} : undefined}
            onChange={onTableChange}
            rowSelection={{
              selectedRowKeys,
              onChange: (selectedRowKeys) => setSelectedRowKeys(selectedRowKeys)
            }}
            pagination={false}
            locale={{
              emptyText: (
                <Empty
                  className="test"
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}
                />
              )
            }}
          />

          {pagination.total > 0 && (
            <div className={styles['pagination-container']}>
              <Pagination
                showSizeChanger={true}
                showQuickJumper={true}
                showTotal={showTotal}
                current={pagination.pageNo}
                pageSize={pagination.pageSize}
                total={pagination.total}
                onChange={(page, pageSize = 20) => {
                  setPagination((prev) => ({
                    ...prev,
                    pageNo: page,
                    pageSize: pageSize
                  }));
                  getConnectionList({
                    pageNo: page,
                    pageSize: pageSize
                  });
                }}
              />
            </div>
          )}
        </>
      )}
      <EditModal
        connection={connection}
        isModalVisible={isModalVisible}
        handleCloseModal={handleCloseModal}
        successCallback={getConnectionList}
      />
    </div>
  );
};

export default Connection;
