/**
 * @file 类型卡片
 * <AUTHOR>
 */

import {FC, useCallback} from 'react';
import cx from 'classnames';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';

interface ITypeCardProps {
  callback: (type) => void; // 点选回调
  type: string; // 类型
  icon: string; // 图片
  color?: string; // 颜色
  isActive?: boolean; // 是否选中
}

const TypeCard: FC<ITypeCardProps> = ({callback, icon, color, type, isActive}) => {
  const handleClick = useCallback(() => {
    callback(type);
  }, [type]);

  return (
    <div className={cx(styles['type-card'], {[styles['type-card-active']]: isActive})} onClick={handleClick}>
      <IconSvg type={icon} color={color || undefined} size={30} className="bordered-circle-icon mb-[8px]" />
      <div className="title">{type}</div>
    </div>
  );
};

export default TypeCard;
