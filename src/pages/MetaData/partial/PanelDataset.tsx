import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import {debounce} from 'lodash';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';

import {CatalogType} from '@api/metaRequest';
import DatasetAndModelFileInfoTable, {
  DatasetAndModelFileRefHandle
} from '../components/DatasetAndModelFileInfoTable';
import CreateDatasetAndModelVersionModal from '../components/CreateDatasetAndModelVersionModal';
import {RULE} from '@utils/regs';
import {OutlinedPlusNew} from 'acud-icon';
import {Privilege, ResourceType} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import PermissionManage from '@components/PermissionManage';
import {isBuildInCatalog} from '../helper';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3'
}

const PanelDataset = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  const dropdownMenu = useMemo(
    () => [
      {key: 'rename', label: '重命名数据集', disable: !canWrite, authName: Privilege.Manage},
      {key: 'remove', label: '删除数据集', disable: !canWrite, authName: Privilege.Manage}
    ],
    [canWrite]
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 文件信息 Table 组件的 Ref
  const fileInfoTableRef = useRef<DatasetAndModelFileRefHandle>(null);

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IDatasetDetailRes>();

  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: '数据集名称',
        value: info.name
      },
      {
        label: '数据集ID',
        value: info.id
      },
      {
        label: '数据集类型',
        value: info.datasetType
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '存储类型',
        value: info.storageType
      },
      {
        label: '数据类型',
        value: Array.isArray(info.dataType) && info.dataType.length > 0 ? info.dataType.join(',') : '-'
      }
    ];
  }, [dataInfo, userList]);

  // 获取详情
  const getDatasetDetail = useCallback(async () => {
    const res = await http.getDatasetDetail(workspaceId, fullName);
    setDataInfo(res.result);
  }, [fullName, workspaceId]);

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getDatasetDetail();
  }, [catalog, schema, node, getDatasetDetail]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey: any) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchDatasetOrModel(workspaceId, http.EnumMetaType.DATASETS, fullName, {comment: text});
      getDatasetDetail();
    },
    [fullName, getDatasetDetail, workspaceId]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, node: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  }, [changeUrlFunReplace]);

  const onDropdownClick = useCallback(
    (key: string) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: node,
          title: 'Dataset',
          metaType: http.EnumMetaType.DATASETS,
          requestFun: http.deleteDatasetOrModel,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [fullName, node, removeSuccessFun]
  );

  // 如果 Tab 页在概览页签，则刷新 「版本信息」列表
  const updataFilelist = debounce(() => {
    fileInfoTableRef?.current?.requestFilelist();
  }, 100);

  // 新建版本弹窗展示
  const [showCreateVersion, setShowCreateVersion] = useState<boolean>(false);

  const [listSize, setListSize] = useState(0);

  const [lastVersion, setLastVersion] = useState(0);

  const renderTab = useMemo(() => {
    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            authList={dataInfo?.privileges || []}
            text={dataInfo?.comment || ''}
            onChangeText={onChangeDescript}
            hasEdit={catalog !== CatalogType.SYSTEM}
          />
          <h2 className="title-head">
            版本信息
            <span className="list-size"> 共{listSize}条</span>
          </h2>
          <div>
            <DatasetAndModelFileInfoTable
              ref={fileInfoTableRef}
              userList={userList}
              metaType={http.EnumMetaType.DATASETS}
              urlState={urlState}
              changeUrlFun={changeUrlFun}
              dataTotalUpdate={(total: number) => setListSize(total)}
              lastVersionUpdate={(ver: number) => setLastVersion(ver)}
              deleteAuths={[Privilege.CreateDatasetVersion]}
              verClickAuths={[Privilege.WriteDataset, Privilege.ReadDataset, Privilege.CreateDatasetVersion]}
            />
          </div>
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Dataset}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getDatasetDetail}
        />
      )
    };
    return config[tab];
  }, [
    catalog,
    changeUrlFun,
    dataInfo?.comment,
    dataInfo?.privileges,
    fullName,
    getDatasetDetail,
    infoList,
    listSize,
    node,
    onChangeDescript,
    tab,
    urlState,
    userList
  ]);

  return (
    <div className="work-meta-volume-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-dataset" size={20} />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        createText="创建数据集版本"
        createIcon={<OutlinedPlusNew width={16} height={16} />}
        onCreateClick={() => setShowCreateVersion(true)}
        authList={dataInfo?.privileges || []}
        createAuthName={Privilege.CreateDatasetVersion}
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges || []}
      />
      {renderTab}
      {/** 重命名 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={node}
        title="Dataset"
        metaType={http.EnumMetaType.DATASETS}
        requestFun={http.patchDatasetOrModel}
        successFun={renameSuccessFun}
        limitLength={64}
        forbidIfLimit
        nameRules={[
          {
            validator: async (_, value) => {
              // 校验特殊字符和长度限制
              if (!RULE.specialNameStartEn64.test(value)) {
                return Promise.reject(new Error(RULE.specialNameStartEn64Text));
              }
              // 异步校验dataset名称是否重复，复用查询接口 silent模式
              const res = await http.getDatasetDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
              if (res.success && res.result?.id) {
                return Promise.reject(new Error('该dataset名称已存在，请重新输入'));
              }
              return Promise.resolve();
            }
          }
        ]}
      />
      {/* 新建版本 */}
      <CreateDatasetAndModelVersionModal
        urlState={urlState}
        currentKey="addDataset"
        version={lastVersion}
        currentModalInfo={dataInfo}
        isModalVisible={showCreateVersion}
        handleCloseModal={() => {
          setShowCreateVersion(false);
        }}
        createdCallback={() => {
          updataFilelist();
        }}
      ></CreateDatasetAndModelVersionModal>
    </div>
  );
};
export default PanelDataset;
