import {forwardRef, useCallback, useEffect, useState, useImperativeHandle, useContext} from 'react';
import {Button, Pagination, Space, Table} from 'acud';
import {useRequest} from 'ahooks';

import * as http from '@api/metaRequest';
import {ColumnsType} from 'acud/lib/table';

import {OutlinedFullScreen, OutlinedFullScreenOut} from 'acud-icon';
import {FullScreen, useFullScreenHandle} from 'react-full-screen';
import styles from './DatasetDataTabInfoTable.module.less';
import cx from 'classnames';
import {PreviewStatus} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';

// 定义通过 ref 暴露的方法类型
export interface DatasetPreviewFileRefHandle {
  requestFilelist: (version: string) => void;
}

const klass = 'volume-file-table';

const ErrMsgContent = {
  [PreviewStatus.FAILED]: ['数据详情预览暂时不可用。', '没有检测到数据集文件或者支持的数据集文件类型。'],
  [PreviewStatus.PROCESSING]: '数据集正在解析，数据详情预览暂时不可用。',
  [PreviewStatus.NODATA]: '当前数据集版本无数据'
};

const DatasetDataTabInfoTable = (props: any, ref: React.Ref<DatasetPreviewFileRefHandle>) => {
  const {currentKey, urlState} = props;
  const {catalog = '', schema = '', node = '', path = '', version} = urlState;

  const metaType = {
    dataset: 'Dataset',
    model: 'Model'
  };
  // 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 虚拟路径
  const virtualPath = `/${metaType[currentKey]}/${catalog}/${schema}/${node}/`;

  useImperativeHandle(ref, () => ({
    // 父组件可调用该方法，重刷列表
    requestFilelist: (version) => {
      setPagination({pageNo: 1, pageSize: 100});
      // getDatasetVersionDataRreview();
    }
  }));

  const [previewDataTotal, setVersionDataTotal] = useState<number>(0);

  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({
    pageNo: 1,
    pageSize: 100
  });

  const [previewDatas, setPreivewDatas] = useState([]);
  const [preivewColumn, setPreivewColumn] = useState<ColumnsType<object>>();
  const [previewStatus, setPreviewStatus] = useState('FAILED');
  const {workspaceId} = useContext(WorkspaceContext);

  // 数据集版本的预览数据
  const {loading, run: getDatasetVersionDataRreview} = useRequest(
    async () => {
      if (!virtualPath) {
        return;
      }
      const res = await http.getDatasetVersionDataRreview(workspaceId, fullName, version, pagination);
      const result: any = res.result || {};
      // 组装表头
      const coloumDatas =
        result?.features
          ?.sort((a, b) => a.featureIdx - b.featureIdx)
          .map((item: any) => {
            return {
              title: item.name,
              dataIndex: item.name,
              key: item.featureIdx,
              ellipsis: true
            };
          }) || [];
      setPreivewColumn([...coloumDatas]);

      // 组装数据
      result.rows = result?.rows?.map((item, index) => ({...item.row, key: index})) || [];
      setPreivewDatas(result.rows);

      // 设置条数
      setVersionDataTotal(result.previewRowsTotal);

      // 设置预览状态
      setPreviewStatus(result.previewStatus);
    },
    {
      manual: true,
      refreshDeps: [catalog, schema, node, pagination, virtualPath]
    }
  );

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getDatasetVersionDataRreview();
  }, [catalog, schema, node, path, getDatasetVersionDataRreview]);

  // 分页方法
  const changePagination = (current, pageSize) => {
    setPagination((pre) => ({...pre, pageNo: current, pageSize: pageSize}));
  };

  useEffect(() => {
    getDatasetVersionDataRreview();
  }, [getDatasetVersionDataRreview, pagination]);

  const [full, setFull] = useState(false);
  const handle = useFullScreenHandle();

  const onFullClick = useCallback((value) => {
    setFull(value);
  }, []);

  return (
    <>
      <FullScreen
        handle={handle}
        onChange={(status) => {
          onFullClick(status);
        }}
      >
        <div className={cx({[styles['fullscreen-content']]: full})}>
          <div className={styles['fullscreen-controll-btn']}>
            <h2 className="title-head">数据信息</h2>
            <Button
              onClick={full ? handle.exit : handle.enter}
              icon={full ? <OutlinedFullScreenOut /> : <OutlinedFullScreen />}
            ></Button>
          </div>
          <div className={`${klass}`}>
            {previewStatus === PreviewStatus.READY && (
              <Table
                loading={loading}
                dataSource={previewDatas || []}
                columns={preivewColumn}
                pagination={false}
                scroll={{x: 800}}
              />
            )}

            {(previewStatus === PreviewStatus.PROCESSING || previewStatus === PreviewStatus.NODATA) && (
              <h2 className={styles['err-msg-title']}>{ErrMsgContent[previewStatus]}</h2>
            )}

            {previewStatus === PreviewStatus.FAILED && (
              <Space direction="vertical">
                {ErrMsgContent[PreviewStatus.FAILED].map((item) => (
                  <h2 className={styles['err-msg-title']}>{item}</h2>
                ))}
              </Space>
            )}

            {/* 可选页码分页: 本期暂不放开 */}
            {previewDataTotal > 0 && (
              <div className={`${klass}-hasPage-patination`}>
                <Pagination
                  showSizeChanger
                  pageSizeOptions={[100]}
                  defaultCurrent={1}
                  defaultPageSize={pagination.pageSize}
                  total={previewDataTotal}
                  onChange={changePagination}
                />
              </div>
            )}
          </div>
        </div>
      </FullScreen>
    </>
  );
};

export default forwardRef(DatasetDataTabInfoTable);
