import React, {forwardRef, useCallback, useContext, useEffect, useImperativeHandle, useState} from 'react';
import {Button, Modal, Pagination, Table, toast} from 'acud';
import {useRequest} from 'ahooks';

import * as http from '@api/metaRequest';
import {formatBytes} from '@utils/utils';
import {ColumnsType} from 'acud/lib/table';

import AuthButton from '@components/AuthComponents/AuthButton';
import {Privilege} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import TextEllipsis from '@components/TextEllipsisTooltip';

// 定义通过 ref 暴露的方法类型
export interface DatasetAndModelFileRefHandle {
  requestFilelist: () => void;
}

const klass = 'volume-file-table';

const DatasetAndModelFileInfoTable = (props: any, ref: React.Ref<DatasetAndModelFileRefHandle>) => {
  const {
    metaType,
    urlState,
    changeUrlFun,
    dataTotalUpdate,
    lastVersionUpdate,
    userList,
    deleteAuths = [],
    verClickAuths = []
  } = props;
  const {catalog = '', schema = '', node = '', path = ''} = urlState;

  // 全名
  const fullName = `${catalog}.${schema}.${node}`;

  const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({
    pageNo: 1,
    pageSize: 10
  });

  const [versionListData, setVersionListData] = useState<
    http.IDatasetVersionDetailRes[] | http.IModelVersionDetailRes[]
  >([]);

  const [versionListTotal, setVersionListTotal] = useState<number>(0);

  const [lastVersion, setLastVersion] = useState(0);

  const [fileAuthList, setFileAuthList] = useState<Privilege[]>([]);

  const {workspaceId} = useContext(WorkspaceContext);

  useImperativeHandle(ref, () => ({
    // 父组件可调用该方法，重刷列表
    requestFilelist: () => {
      setPagination((pre) => ({...pre, pageNo: 1}));
      getDatasetOrModelVersionList();
    }
  }));

  // 请求 数据集或者模型的版本列表
  const {loading, run: getDatasetOrModelVersionList} = useRequest(
    async () => {
      let res = null;
      if (metaType === http.EnumMetaType.DATASETS) {
        res = await http.getDatasetVersionList(workspaceId, fullName, pagination);
      } else {
        res = await http.getModelVersionList(workspaceId, fullName, pagination);
      }
      const result: any = res.result || {};
      result.versions = result?.versions?.map((item: any) => ({...item, key: item.id})) || [];
      setVersionListData(result.versions);
      const lastVersion = Number(result.versions[0]?.name?.slice(1)) || 0;
      setLastVersion(lastVersion);
      result.total = result?.total || 0;
      setVersionListTotal(result.total);
      setFileAuthList(result.privileges || []);
    },
    {
      manual: true,
      refreshDeps: [catalog, schema, node, pagination]
    }
  );

  // 初始化
  useEffect(() => {
    if (!(catalog && schema && node)) {
      return;
    }
    getDatasetOrModelVersionList();
  }, [catalog, schema, node, path, getDatasetOrModelVersionList]);

  // 版本删除方法
  const deleteDatasetOrModelVersion = async (versionName: string) => {
    const res = await http.deleteDatasetOrModelVersion(workspaceId, metaType, fullName, versionName);
    if (res.success) {
      // 重新刷新当前列表 跳转首页
      changePagination(1, pagination.pageSize);
    }
  };

  const onVersionDelete = (version?: any) => {
    Modal.confirm({
      title: '确定要删除吗？',
      content: version ? (
        <div>删除后⽆法恢复！请确定是否要删除版本 “{version?.name}”</div>
      ) : (
        <div>删除后⽆法恢复！请确定是否要删除所选择的内容</div>
      ),
      onOk: () =>
        version.name
          ? deleteDatasetOrModelVersion(version.name)
          : toast.error({
            message: '版本号缺失，无法进行删除操作',
            duration: 3
          })
    });
  };

  // 分页方法
  const changePagination = (current, pageSize) => {
    setPagination((pre) => ({...pre, pageNo: current, pageSize: pageSize}));
  };

  useEffect(() => {
    getDatasetOrModelVersionList();
  }, [getDatasetOrModelVersionList, pagination]);

  useEffect(() => {
    dataTotalUpdate(versionListTotal);
    lastVersionUpdate(lastVersion);
  }, [dataTotalUpdate, versionListTotal]);

  const goToVersionInfo = useCallback(
    (version: string) => {
      changeUrlFun((pre) => ({...pre, version}));
    },
    [changeUrlFun]
  );

  const datasetColumns: ColumnsType<object> = [
    {
      title: '版本信息',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (text: string) => {
        return (
          <AuthButton
            isAuth={fileAuthList.length && verClickAuths.some((item: any) => fileAuthList.includes(item))}
            type="actiontext"
            onClick={() => {
              goToVersionInfo(text);
            }}
          >
            {text}
          </AuthButton>
        );
      }
    },
    {
      title: '数据格式',
      dataIndex: 'dataFormat',
      key: 'dataFormat',
      width: 100,
      render: (value: string) => {
        return value || '-';
      }
    },
    {
      title: '样本数',
      dataIndex: 'sampleCount',
      key: 'sampleCount',
      width: 100,
      render: (value: number) => {
        return value > 0 ? value : '-';
      }
    },
    {
      title: '数据集大小',
      dataIndex: 'datasetSize',
      key: 'datasetSize',
      width: 100,
      render: (value: number) => {
        return value > 0 ? formatBytes(value) : '-';
      }
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 160,
      render: (value: string) => userList.find((item) => item.id === value)?.name || value
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (value: string) => value || '-'
    },
    {
      title: '修改人',
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      width: 160,
      render: (value: string) => userList.find((item) => item.id === value)?.name || value
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => value || '-'
    },
    {
      title: '描述',
      dataIndex: 'comment',
      key: 'comment',
      width: 200,
      ellipsis: true,
      render: (value: string) => value || '-'
    },
    {
      title: '操作',
      dataIndex: '',
      width: 200,
      fixed: 'right',
      key: 'x',
      render: (_: any) => {
        return (
          <div className={'file-table-action'}>
            <AuthButton
              isAuth={fileAuthList.length && deleteAuths.some((item: any) => fileAuthList.includes(item))}
              type="actiontext"
              onClick={() => onVersionDelete(_)}
            >
              删除
            </AuthButton>
          </div>
        );
      }
    }
  ];

  const modelColumns: ColumnsType<object> = [
    {
      title: '版本信息',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (text: string) => {
        return (
          <AuthButton
            isAuth={fileAuthList.length && verClickAuths.some((item: any) => fileAuthList.includes(item))}
            type="actiontext"
            onClick={() => {
              goToVersionInfo(text);
            }}
          >
            {text}
          </AuthButton>
        );
      }
    },
    {
      title: '基础模型',
      dataIndex: 'baseModel',
      key: 'baseModel',
      width: 100,
      render: (value) => {
        const textCon = value || '-';
        return (<TextEllipsis tooltip={textCon}>{textCon}</TextEllipsis>)
      },
    },
    // {
    //   title: '模型大小',
    //   dataIndex: 'modelSize',
    //   key: 'modelSize',
    //   width: 100,
    //   render: (value: number) => {
    //     return value > 0 ? formatBytes(value) : '-';
    //   }
    // },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 160,
      render: (value: string) => userList.find((item) => item.id === value)?.name || value
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (value: string) => value || '-'
    },
    {
      title: '修改人',
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      width: 160,
      render: (value: string) => userList.find((item) => item.id === value)?.name || value
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 160,
      render: (value: string) => value || '-'
    },
    {
      title: '描述',
      dataIndex: 'comment',
      key: 'comment',
      width: 200,
      ellipsis: true,
      render: (value: string) => value || '-'
    },
    {
      title: '操作',
      dataIndex: '',
      width: 200,
      fixed: 'right',
      key: 'x',
      render: (_: any) => {
        return (
          <div className={'file-table-action'}>
            <AuthButton
              isAuth={fileAuthList.length && deleteAuths.some((item: any) => fileAuthList.includes(item))}
              type="actiontext"
              onClick={() => onVersionDelete(_)}
            >
              删除
            </AuthButton>
          </div>
        );
      }
    }
  ];

  return (
    <div className={`${klass}`}>
      <Table
        loading={loading}
        dataSource={versionListData}
        columns={metaType === http.EnumMetaType.DATASETS ? datasetColumns : modelColumns}
        pagination={false}
        scroll={{x: 800}}
      />
      {/* 可选页码分页 */}
      <div className={`${klass}-hasPage-patination`}>
        <Pagination
          showSizeChanger
          pageSizeOptions={[10, 30, 50, 100]}
          onShowSizeChange={changePagination}
          defaultCurrent={1}
          total={versionListTotal}
          onChange={changePagination}
        />
      </div>
    </div>
  );
};

export default forwardRef(DatasetAndModelFileInfoTable);
