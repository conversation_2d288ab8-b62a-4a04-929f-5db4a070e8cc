/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-05-29 15:38:01
 * @LastEditTime: 2025-07-15 19:42:37
 * @FilePath: /console-databuilder/src/pages/MetaData/components/CreateDatasetAndModelModal.tsx
 */

import {FC, useCallback, useContext, useState} from 'react';
import {Form, Input, Link, Modal, toast, Radio, Select} from 'acud';
import {
  EVolumeType,
  createDataset,
  createModel,
  IDatasetParams,
  IModelParams,
  getDatasetDetail,
  getModelDetail
} from '@api/metaRequest';
import {RULE} from '@utils/regs';
import {IUrlStateHandler} from '../index';
import styles from './CreateDatasetAndModelModal.module.less';
const {Option} = Select;

import flags from '@/flags';

const isPrivate = flags.DatabuilderPrivateSwitch;

import {DatasetAndModelFieldChineseMap} from '../config';
import {WorkspaceContext} from '@pages/index';
interface ICreateDatasetAndModelModalProps {
  /** dataset 或者 model */
  currentKey: string;
  /** url参数信息 */
  urlState: IUrlStateHandler['urlState'];
  /** 弹窗是否打开 */
  isModalVisible: boolean;
  /**
   * 创建成功的回调函数
   * @param name 当前 modal 的名称
   * @returns
   */
  createdCallback: (name: string) => void;
  /** 关闭弹窗 */
  handleCloseModal: () => void;
}

enum ModalKey {
  addDataset = 'addDataset',
  addModel = 'addModel'
}

const STORE_NAME = isPrivate ? 'HDFS' : 'BOS';

const createFormContents = {
  [ModalKey.addDataset]: {
    form: {
      controlType: 'datasetType',
      dataType: 'dataType',
      storageType: 'storageType'
    },
    name: '数据集',
    title: '创建数据集',
    controlType: {
      [EVolumeType.MANAGED]: 'Managed 数据集',
      [EVolumeType.EXTERNAL]: 'External 数据集'
    },
    tooltip: `Managed数据集的数据存储在工作空间的元存储中，External数据集需要用户挂载其他${STORE_NAME}、PFS等存储路径，数据存储在对应${STORE_NAME}、PFS等存储中`,
    inputPlaceholder: '请输入数据集描述'
  },
  [ModalKey.addModel]: {
    form: {
      controlType: 'modelType',
      dataType: 'dataType',
      storageType: 'storageType'
    },
    name: '模型',
    title: '创建模型',
    controlType: {
      [EVolumeType.MANAGED]: 'Managed 模型',
      [EVolumeType.EXTERNAL]: 'External 模型'
    },
    tooltip: `Managed模型的数据存储在工作空间的元存储中，External模型需要用户挂载其他${STORE_NAME}路径，数据存储在对应${STORE_NAME}中`,
    inputPlaceholder: '请输入描述'
  }
};

const CreateDatasetAndModelModal: FC<ICreateDatasetAndModelModalProps> = ({
  currentKey,
  urlState,
  isModalVisible,
  createdCallback,
  handleCloseModal
}) => {
  const currentModal = createFormContents[currentKey];
  const currentModalName = currentModal.name;
  const {catalog = '', schema = ''} = urlState;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const datasetType = Form.useWatch(currentModal.form.controlType, form);

  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  const createContentCommon = useCallback(
    (values: any) => {
      if (currentKey === 'addDataset') {
        const params: IDatasetParams = {
          ...values,
          catalogName: catalog,
          schemaName: schema,
          ...(values.storageLocation ? {storageLocation: 'bos://' + values.storageLocation} : {})
        };
        setLoading(true);
        // 创建 dataset
        createDataset(workspaceId, params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: '创建成功',
                duration: 5
              });

              onCloseModal();
              // 调用成功的回调方法
              createdCallback(values.name);
            }
          })
          .finally(() => {
            setLoading(false);
          });
        return;
      }
      if (currentKey === 'addModel') {
        const params: IModelParams = {
          ...values,
          catalogName: catalog,
          schemaName: schema,
          ...(values.storageLocation ? {storageLocation: 'bos://' + values.storageLocation} : {})
        };
        setLoading(true);
        // 创建 model
        createModel(workspaceId, params)
          .then((res) => {
            if (res?.success) {
              toast.success({
                message: '创建成功',
                duration: 5
              });

              onCloseModal();
              // 调用成功的回调方法
              createdCallback(values.name);
            }
          })
          .finally(() => {
            setLoading(false);
          });
        return;
      }
      console.log('invalid params currentKey');
    },
    [catalog, createdCallback, currentKey, onCloseModal, schema]
  );

  // 提交表单
  const handleConfirm = useCallback(() => {
    // 表单校验
    form
      .validateFields()
      .then((values) => {
        createContentCommon(values);
      })
      .catch(() => {});
  }, [form, createContentCommon]);

  const renderDataTypeOptions = () => {
    return Object.entries(DatasetAndModelFieldChineseMap).map(([optKey, optValue]) => {
      return (
        <Option key={optKey} value={optKey}>
          {optValue}
        </Option>
      );
    });
  };
  return (
    <Modal
      closable={true}
      title={currentModal.title}
      width={500}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
      className={styles['create-combine-modal']}
    >
      <Form
        labelAlign="left"
        layout="vertical"
        colon={false}
        labelWidth={80}
        initialValues={{[currentModal.form.controlType]: EVolumeType.MANAGED}}
        form={form}
      >
        <Form.Item
          label={`${currentModalName}名称`}
          name="name"
          validateFirst
          validateDebounce={1000}
          rules={[
            {required: true, message: `请输入${currentModalName}名称`},
            {
              validator: async (_, value) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialNameStartEn64.test(value)) {
                  return Promise.reject(new Error(RULE.specialNameStartEn64Text));
                }
                // 异步校验dataset 或者 model 名称是否重复，复用查询接口 silent模式
                if (currentKey === 'addDataset') {
                  const res = await getDatasetDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
                  if (res.success && res.result?.id) {
                    return Promise.reject(new Error('该dataset名称已存在，请重新输入'));
                  }
                  return Promise.resolve();
                }
                if (currentKey === 'addModel') {
                  const res = await getModelDetail(workspaceId, `${catalog}.${schema}.${value}`, true);
                  if (res.success && res.result?.id) {
                    return Promise.reject(new Error('该model名称已存在，请重新输入'));
                  }
                  return Promise.resolve();
                }
                return Promise.reject(new Error('invalid params currentKey~'));
              }
            }
          ]}
        >
          <Input
            placeholder={`请输入${currentModalName}名称`}
            allowClear
            limitLength={64}
            forbidIfLimit={true}
          />
        </Form.Item>

        <Form.Item
          label={`${currentModalName}类型`}
          name={currentModal.form.controlType}
          required
          tooltip={currentModal.tooltip}
        >
          <Radio.Group>
            {Object.keys(currentModal.controlType).map((key) => (
              <Radio value={key} key={key}>
                <span>{currentModal.controlType[key]}</span>
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        {datasetType === EVolumeType.EXTERNAL && (
          <>
            <Form.Item
              label="存储类型"
              name={currentModal.form.storageType}
              required
              tooltip="需要挂载的外部存储类型"
              initialValue={isPrivate ? 'HDFS' : 'BOS'}
            >
              <Select style={{width: '100%'}}>
                {isPrivate ? (
                  <Option value="HDFS">文件存储HDFS</Option>
                ) : (
                  <Option value="BOS">对象存储BOS</Option>
                )}
                {/* <Option value="PFS">并行文件存储PFS</Option> */}
              </Select>
            </Form.Item>
          </>
        )}

        {currentKey === ModalKey.addDataset && (
          <Form.Item
            label="数据类型"
            name={currentModal.form.dataType}
            tooltip="数据集中的数据类型，支持多选"
            rules={[
              {required: true, message: '请选择数据类型'},
              {
                validator: async (_, value) => {
                  // 校验特殊字符和长度限制
                  if (!value || value.length === 0) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <Select placeholder={'请选择数据类型'} style={{width: '100%'}} mode="multiple" allowClear>
              {renderDataTypeOptions()}
            </Select>
          </Form.Item>
        )}

        <Form.Item label="描述" name="comment">
          <Input.TextArea
            placeholder={currentModal.inputPlaceholder}
            allowClear
            limitLength={150}
            autoSize={{minRows: 2, maxRows: 4}}
            forbidIfLimit
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateDatasetAndModelModal;
