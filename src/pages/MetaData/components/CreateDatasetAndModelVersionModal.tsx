/*
 * @Author: hong<PERSON><PERSON><EMAIL>
 * @Date: 2025-05-29 15:38:01
 * @LastEditTime: 2025-07-15 19:43:39
 * @FilePath: /console-databuilder/src/pages/MetaData/components/CreateDatasetAndModelVersionModal.tsx
 */

import {FC, useCallback, useMemo, useState, useContext} from 'react';
import {Button, Cascader, Form, Input, Link, Modal, Select, toast} from 'acud';
import {
  EVolumeType,
  createDataseVersion,
  createModelVersion,
  IDatasetVersionParams,
  IModelVersionParams,
  IDatasetDetailRes
} from '@api/metaRequest';
import {RULE} from '@utils/regs';
import {IUrlStateHandler} from '../index';
import styles from './CreateDatasetAndModelModal.module.less';

import {DatasetAndModelParseFiles, DatasetAndModelFieldEnum, ModelBaseModel} from '../config';
import {IModelDetailRes} from '../../../api/metaRequest';
import * as base from '@jupyter-widgets/base';
import {WorkspaceContext} from '@pages/index';
import {OutlinedDown, OutlinedUp} from 'acud-icon';
import flags from '@/flags';
const isPrivate = flags.DatabuilderPrivateSwitch;

interface ICreateDatasetAndModelVersionModalProps {
  /** dataset 或者 model */
  currentKey: string;
  // 当前最大版本号
  version: number;
  // 当前 数据集&模型类型 的内容详情
  currentModalInfo: IDatasetDetailRes | IModelDetailRes;
  /** url参数信息 */
  urlState: IUrlStateHandler['urlState'];
  /** 弹窗是否打开 */
  isModalVisible: boolean;
  /**
   * 创建成功的回调函数
   * @param name 当前 version 的名称
   * @returns
   */
  createdCallback: (name: string) => void;
  /** 关闭弹窗 */
  handleCloseModal: () => void;
}

enum ModalKey {
  addDataset = 'addDataset',
  addModel = 'addModel'
}

const createFormContents = {
  [ModalKey.addDataset]: {
    name: '数据集',
    title: '创建数据集版本',
    inputPlaceholder: '请输入内容',
    modelType: 'datasetType'
  },
  [ModalKey.addModel]: {
    name: '模型',
    title: '创建模型版本',
    inputPlaceholder: '请输入内容',
    modelType: 'modelType'
  }
};

const {OptGroup, Option} = Select;

const CreateDatasetAndModelVersionModal: FC<ICreateDatasetAndModelVersionModalProps> = ({
  currentKey,
  version,
  urlState,
  isModalVisible,
  currentModalInfo,
  createdCallback,
  handleCloseModal
}) => {
  const currentModal = createFormContents[currentKey];
  const {name: currentModalName, modelType} = currentModal;
  const {catalog = '', schema = ''} = urlState;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 工作空间id
  const {workspaceId} = useContext(WorkspaceContext);
  const lastVersion = useMemo(() => {
    return `V${++version}`;
  }, [version]);

  const [openCascader, setOpenCascader] = useState<boolean>(false);

  const [baseModelList, setBaseModelList] = useState(ModelBaseModel);

  // 关闭弹窗
  const onCloseModal = useCallback(() => {
    handleCloseModal();
    form.resetFields();
  }, [form, handleCloseModal]);

  const createContentCommon = useCallback(
    (values: any) => {
      // 私有化使用 hdfs，公有云使用 bos
      const storageLocation = isPrivate
        ? `hdfs://${values.storageLocation}`
        : `bos://${values.storageLocation}`;

      const params: IDatasetVersionParams | IModelVersionParams = {
        ...values,
        catalogName: catalog,
        schemaName: schema,
        name: currentModalInfo?.name || '',
        ...(values.storageLocation ? {storageLocation} : {})
      };

      setLoading(true);

      let createResponse: Promise<any>;
      // 创建 dataset 或者 model
      switch (currentKey) {
        case 'addDataset':
          createResponse = createDataseVersion(workspaceId, params as IDatasetVersionParams);
          break;
        case 'addModel':
          createResponse = createModelVersion(workspaceId, params as IModelVersionParams);
          break;
        default:
          break;
      }

      createResponse
        .then((res) => {
          if (res?.success) {
            toast.success({
              message: '创建成功',
              duration: 5
            });

            onCloseModal();
            // 调用成功的回调方法
            createdCallback(values.name);
          }
        })
        .catch((err) => {
          console.error('创建失败：', err);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [catalog, createdCallback, currentKey, currentModalInfo, onCloseModal, schema, workspaceId]
  );

  // 提交表单
  const handleConfirm = useCallback(() => {
    // 表单校验
    form
      .validateFields()
      .then((values) => {
        createContentCommon(values);
      })
      .catch(() => {});
  }, [form, createContentCommon]);

  // 跳转accesslist页面
  const jumpAccesslist = () => {
    window.open('/iam/#/iam/accesslist', '_blank');
  };

  const renderDatasetDataTypeSelect = () => {
    const dataTypeValue = DatasetAndModelFieldEnum.TEXT;
    const selectConfigs = DatasetAndModelParseFiles[dataTypeValue];
    return (
      <Form.Item
        label="数据解析格式"
        name="dataFormat"
        required
        tooltip={`当前数据集支持的数据类型为${dataTypeValue}`}
      >
        <Select placeholder={'请选择数据解析格式'} options={selectConfigs} style={{width: '100%'}}></Select>
      </Form.Item>
    );
  };

  const cascaderStatusChange = () => {
    setOpenCascader(!openCascader);
  };

  const inputOnchange = (value) => {
    if (!openCascader) {
      setOpenCascader(true);
    }
    if (!value) {
      setBaseModelList(ModelBaseModel);
      return;
    }
    const baseModelTemp = ModelBaseModel.filter((item: any) => {
      const tag = item.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      if (tag) {
        return tag;
      }
      const cTag = item.children.some((cItem: any) => {
        return cItem.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      });
      return cTag;
    });
    setBaseModelList(baseModelTemp);
  };

  return (
    <Modal
      closable={true}
      title={currentModal.title}
      width={500}
      visible={isModalVisible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
      className={styles['create-combine-modal']}
    >
      <Form
        labelAlign="left"
        layout="vertical"
        colon={false}
        labelWidth={80}
        form={form}
        style={{position: 'relative'}}
      >
        {/* <Form.Item label="版本" name="version" initialValue={lastVersion}>
          <Input readOnly disabled />
        </Form.Item> */}

        {/* 只有数据集使用 */}
        {currentKey === ModalKey.addDataset && renderDatasetDataTypeSelect()}

        {/* 只有模型使用 */}
        {currentKey === ModalKey.addModel && (
          <Form.Item noStyle>
            <Form.Item
              label="基础模型"
              name="baseModel"
              tooltip="基础模型"
              rules={[
                {required: true, message: '请输入或选择基础模型'},
                {
                  validator: (_, value) => {
                    if (/[\u4e00-\u9fa5\s]/.test(value)) {
                      return Promise.reject(new Error('基础模型不能包含中文和空格'));
                    }
                    return value?.length > 128
                      ? Promise.reject(new Error('最大长度不可超过128个字符'))
                      : Promise.resolve();
                  }
                }
              ]}
            >
              <Input
                autoComplete="off"
                placeholder="请输入"
                limitLength={128}
                allowClear
                addonAfter={
                  <Button
                    style={{border: 'none'}}
                    onClick={() => {
                      cascaderStatusChange();
                    }}
                    icon={openCascader ? <OutlinedDown /> : <OutlinedUp />}
                  ></Button>
                }
                onChange={(e) => {
                  inputOnchange(e.target.value);
                }}
                onFocus={(e) => {
                  inputOnchange(e.target.value);
                }}
                onBlur={() => {
                  setOpenCascader(false);
                }}
              />
            </Form.Item>
            <Cascader
              placeholder={'请选择所需的基础模型'}
              style={{width: '100%', visibility: 'hidden', position: 'absolute', top: 21, left: 0}}
              options={baseModelList}
              popupVisible={openCascader}
              onChange={(value) => {
                form.setFieldsValue({baseModel: value.pop()});
                setOpenCascader(false);
              }}
            />
          </Form.Item>
        )}

        {currentModalInfo &&
          currentModalInfo[modelType] === EVolumeType.EXTERNAL &&
          (isPrivate ? (
            <Form.Item
              label="存储位置"
              name="storageLocation"
              rules={[
                {required: true, message: '请输入HDFS路径'},
                {pattern: RULE.hdfsDefaultFs, message: RULE.hdfsText},
                {
                  validator: async (_, value) => {
                    if (value && value.length > 493) {
                      return Promise.reject(new Error('HDFS路径长度不能超过500'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
              keepDisplayExtra
              extra={
                <div>
                  <span>
                    填写hdfs://fs.defaultFS参数值/目录名称，其中“fs.defaultFS参数值”从HDFS的core-site.xml文件中获取
                  </span>
                </div>
              }
            >
              <Input placeholder="请输入HDFS路径" addonBefore="hdfs://" />
            </Form.Item>
          ) : (
            <>
              <Form.Item
                label="BOS路径"
                name="storageLocation"
                rules={[
                  {required: true, message: '请输入BOS路径'},
                  {pattern: RULE.bos, message: RULE.bosText},
                  {
                    validator: async (_, value) => {
                      if (value && value.length > 494) {
                        return Promise.reject(new Error('BOS路径长度不能超过500'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
                keepDisplayExtra
                extra={
                  <div>
                    <span>BOS路径需要和工作空间在同一个地域</span>
                  </div>
                }
              >
                <Input placeholder="请输入BOS路径" addonBefore="bos://" />
              </Form.Item>
              <Form.Item
                label="AccessKey"
                name="accessKeyId"
                rules={[{required: true, message: '请输入AccessKey'}]}
                keepDisplayExtra
                extra={
                  <div>
                    <span>{`建议AK/SK有BOSFullControl权限，权限不足会影响${currentModalName}的读写操作。`}</span>
                    <Link onClick={jumpAccesslist}>立即获取</Link>
                  </div>
                }
              >
                <Input placeholder="请输入AccessKey" allowClear />
              </Form.Item>

              <Form.Item
                label="SecretKey"
                name="secretAccessKey"
                rules={[{required: true, message: '请输入SecretKey'}]}
              >
                <Input placeholder="请输入SecretKey" allowClear />
              </Form.Item>
            </>
          ))}

        <Form.Item label="描述" name="comment">
          <Input.TextArea
            placeholder={currentModal.inputPlaceholder}
            allowClear
            limitLength={150}
            autoSize={{minRows: 2, maxRows: 4}}
            forbidIfLimit
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateDatasetAndModelVersionModal;
