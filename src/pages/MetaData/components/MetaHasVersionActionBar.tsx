/*
 * @Author: ho<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2025-06-04 15:39:39
 * @LastEditTime: 2025-06-22 15:36:00
 * @FilePath: /console-databuilder/src/pages/MetaData/components/MetaHasVersionActionBar.tsx
 */

import React, {useState} from 'react';
import {Button, Space, Select} from 'acud';

const {Option} = Select;

import styles from './MetaAction.module.less';
import AuthButton from '@components/AuthComponents/AuthButton';
import {CatalogType} from '@api/metaRequest';
import {isBuildInCatalog} from '../helper';

interface IMetaActionBar {
  catalog: CatalogType.SYSTEM | CatalogType.EDAP_DATALAKE | string; // 当 catalog 为 内置的 system 和 EDAPDataLake 时，禁用相关操作
  icon: React.ReactNode; // 标题左侧图标
  currentVersion: string; // 当前版本号
  versions: Array<{key: string; value: string}>; // 版本号列表
  versionsLoading: boolean;
  scrollLoadVersion?: (key?: any) => void; // 滚动加载事件
  createText: string; // 新建按钮文本
  createIcon?: React.ReactNode; // 新建按钮图标
  onCreateClick: (key?: string) => void; // 新建按钮点击事件
  deleteIcon?: React.ReactNode; // 删除按钮图标
  onDeleteClick?: (key?: string) => void; // 删除按钮点击事件
  changeVersionFun: (key: string) => void; // 切换版本事件
  authList: Array<string>; // 权限列表
  deleteAuths: Array<string>; // 删除权限点列表
  uploadAuths: Array<string>; // 上传权限点列表
}

const MetaHasVersionActionBar = (props: IMetaActionBar) => {
  const {
    catalog,
    icon,
    currentVersion,
    versions,
    versionsLoading,
    scrollLoadVersion,
    createText,
    onCreateClick,
    createIcon,
    deleteIcon,
    onDeleteClick,
    changeVersionFun,
    authList,
    deleteAuths,
    uploadAuths
  } = props;

  // 内置的 system 和 EDAPDataLake 时，禁用相关操作
  const isDisabled = isBuildInCatalog(catalog as CatalogType);

  const [borderShow, setBorderShow] = useState(false);

  return (
    <div className={styles['meta-action']}>
      <div className={styles['meta-action-title']}>
        <span>{icon}</span>
        <Select
          style={{width: 90}}
          loading={versionsLoading}
          bordered={borderShow}
          defaultValue={currentVersion}
          listHeight={288}
          onPopupScroll={(e) => {
            scrollLoadVersion(e.target);
          }}
          onMouseEnter={() => setBorderShow(true)}
          onBlur={() => setBorderShow(false)}
          onMouseLeave={() => setBorderShow(false)}
          onChange={(version) => {
            changeVersionFun(version);
          }}
        >
          {versions.map((item) => (
            <Option key={item.key} value={item.value}>
              {item.value}
            </Option>
          ))}
        </Select>
      </div>
      <div className={styles['meta-action-button-group']}>
        <Space>
          <AuthButton onClick={() => onDeleteClick()} disabled={isDisabled} icon={deleteIcon}
            isAuth={authList.length &&
              deleteAuths.some((item: string) => authList.includes(item))
            } >

          </AuthButton>
          <AuthButton type="primary" onClick={() => onCreateClick()} disabled={isDisabled} icon={createIcon}
            isAuth={authList.length &&
              uploadAuths.some((item: string) => authList.includes(item))
            }>
            {createText}
          </AuthButton>
        </Space>
      </div>
    </div >
  );
};

export default React.memo(MetaHasVersionActionBar);
