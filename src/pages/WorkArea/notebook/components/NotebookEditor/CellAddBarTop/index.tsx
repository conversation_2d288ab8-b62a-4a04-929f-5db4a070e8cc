import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import {cellMetaKey} from '@pages/WorkArea/notebook/config';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands, index} = props;

  function onInsertAbove(type) {
    return () => {
      const cellType = type === 'Markdown' ? 'markdown' : 'code';
      commands
        .execute(NotebookCommandIds.insertAbovePro, {cellType, metadata: {[cellMetaKey]: {language: type}}})
        .catch((reason) => {
          console.error('Failed to insert above.', reason);
        });
    };
  }

  return (
    <div className={cx('cell-add-bar-top')}>
      <div className={cx('cell-add-bar-content')}>
        <div className={cx('item')} onClick={onInsertAbove('Python')}>
          +Python
        </div>
        <div className={cx('item')} onClick={onInsertAbove('SQL')}>
          +SQL
        </div>
        <div className={cx('item')} onClick={onInsertAbove('Markdown')}>
          +Markdown
        </div>
      </div>
    </div>
  );
}
