:global(.dla-Box-Notebook .jp-Cell.jp-mod-active) {
  .cell-title-bar {
    display: flex;
  }
}

.cell-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  position: relative;

  .run {
    width: 24px;
    height: 24px;
    // background-color: #2468F2;
    // cursor: pointer;
    background-image: url(../../../../../../assets/originSvg/notebook/triangle.svg?url);
    background-size: 12px 12px;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 4px;
  }

  .cell-title {
    max-width: 200px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    .title-text {
      cursor: pointer;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .cell-right-action {
    display: flex;
    align-items: center;
    gap: 12px;

    .language-selector {
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      min-width: 60px;
      text-align: center;
      border: 1px solid #e0e0e0;
      background-color: #f5f5f5;

      &:hover {
        background-color: #e8e8e8;
        border-color: #d0d0d0;
      }

      .tips {
        color: red;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid red;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
      }
    }

    .more {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background-image: url(../../../../../../assets/originSvg/notebook/more.svg?url);
      background-size: 14px 14px;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
}
