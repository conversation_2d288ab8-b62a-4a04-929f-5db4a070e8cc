import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands, model} = props;

  function onDelete() {
    return () => {
      commands.execute(NotebookCommandIds.deleteCells).catch((reason) => {
        console.error('Failed to delete cells.', reason);
      });
    };
  }

  return <div className={cx('cell-delete-bar')} onClick={onDelete()}></div>;
}
