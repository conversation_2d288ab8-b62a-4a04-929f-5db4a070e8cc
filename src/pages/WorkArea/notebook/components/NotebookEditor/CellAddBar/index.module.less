.cell-add-bar-wrapper {
  height: 36px;
  width: 100%;
  position: absolute!important;
  bottom: 0;
  left: 0;
  transform: translateY(100%);
  display: flex;
  align-items: center;
}

.cell-add-bar {
  width: 100%;
  height: 100%;
  z-index: 1;
  &:hover {
    .cell-add-bar-content {
      display: flex;
    }
  }
  .cell-add-bar-content {
    display: none;
    justify-content: center;
    align-items: center;
    height: 100%;
    &::before {
      content: '';
      border-top: 1px dashed #D4D6D9;
      flex: 1;
      margin-right: 12px;
    }
    &::after {
      content: '';
      border-top: 1px dashed #D4D6D9;
      flex: 1;
      margin-left: 12px;
    }

    .item {
      margin-right: 32px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

