import classNames from 'classnames/bind';
import styles from './index.module.less';
import {ICellSidebarProps, NotebookCommandIds} from '@baidu/db-jupyter-react/lib/components/notebook';
import {cellMetaKey} from '@pages/WorkArea/notebook/config';

const cx = classNames.bind(styles);

export default function CellAddBar(props: ICellSidebarProps) {
  const {commands} = props;

  function onInsertBelow(type) {
    return () => {
      const cellType = type === 'Markdown' ? 'markdown' : 'code';
      commands
        .execute(NotebookCommandIds.insertBelowPro, {cellType, metadata: {[cellMetaKey]: {language: type}}})
        .catch((reason) => {
          console.error('Failed to insert below.', reason);
        });
    };
  }

  return (
    <div className={cx('cell-add-bar')}>
      <div className={cx('cell-add-bar-content')}>
        <div className={cx('item')} onClick={onInsertBelow('Python')}>
          +Python
        </div>
        <div className={cx('item')} onClick={onInsertBelow('SQL')}>
          +SQL
        </div>
        <div className={cx('item')} onClick={onInsertBelow('Markdown')}>
          +Markdown
        </div>
      </div>
    </div>
  );
}
