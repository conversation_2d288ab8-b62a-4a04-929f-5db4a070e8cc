import React, {useState} from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import MetaDataTree from './components/MetaDataTree';
import HotkeysHelp from './components/HotkeysHelp';
import {Tooltip} from 'acud';

const cx = classNames.bind(styles);

export default function LeftSidebar() {
  const [activeKey, setActiveKey] = useState('');

  // 快捷键帮助弹窗状态
  const [hotkeysHelpVisible, setHotkeysHelpVisible] = useState(false);

  const sidebarItems = [
    {
      key: 'meta-data',
      label: '元数据'
    }
  ];

  function onIconClick(key: string) {
    setActiveKey((pre) => (pre === key ? '' : key));
  }

  return (
    <div className={cx('left-sidebar')}>
      <div className={cx('sidebar-icons-wrap')}>
        <div className={cx('sidebar-icons')}>
          {sidebarItems.map((item) => {
            return (
              <Tooltip key={item.key} title={item.label} placement="right">
                <div
                  className={cx('sidebar-icon', item.key, {active: activeKey === item.key})}
                  onClick={() => onIconClick(item.key)}
                ></div>
              </Tooltip>
            );
          })}
        </div>
        <Tooltip title="快捷键" placement="right">
          <div
            className={cx('sidebar-icon', 'sidebar-keymap')}
            onClick={() => setHotkeysHelpVisible(true)}
          ></div>
        </Tooltip>
      </div>
      <div className={cx('sidebar-content', {hide: !activeKey})}>
        {activeKey === 'meta-data' ? <MetaDataTree onClose={() => setActiveKey('')} /> : null}
      </div>
      <HotkeysHelp visible={hotkeysHelpVisible} onClose={() => setHotkeysHelpVisible(false)} />
    </div>
  );
}
