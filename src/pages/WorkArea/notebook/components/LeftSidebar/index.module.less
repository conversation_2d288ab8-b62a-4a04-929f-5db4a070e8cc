.left-sidebar {
  display: flex;
  height: 100%;
  .sidebar-icons-wrap {
    display: flex;
    flex-direction: column;
    width: 40px;
    height: 100%;
    justify-content: space-between;
    border-right: 1px solid rgba(212, 214, 217, 0.6);
    align-items: center;
    padding: 8px 0;
  }
  .sidebar-icons {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

  }
  .sidebar-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;

    &:hover {
      background-color: rgba(#070c14, 0.06);
    }

    &.active {
      background-color: rgba(#070c14, 0.06);
    }
  }

  .meta-data {
    background-image: url("~@assets/originSvg/notebook/meta.svg?url");
  }

  .sidebar-keymap {
    background-image: url("~@assets/originSvg/notebook/keymap.svg?url");
  }

  .sidebar-content {
    width: 300px;
    flex: 1;
    border-right: 1px solid rgba(212, 214, 217, 0.6);
    padding: 8px 16px;

    &.hide {
      display: none;
    }
  }
}
