.meta-data-tree {
  position: relative;
  height: 100%;

  .tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      color: #151B26;
      font-weight: 500;
      font-size: 14px;
    }
  }
  .search {
    margin-bottom: 12px;
  }


  :global(.acud-tree-title-titleWithOps) {
    width: 100%;
  }
  :global(.acud-tree-title-titleContent) {
    position: relative;
    width: 100%;
    padding-right: 12px;
  }

  .title-right {
    position: absolute;
    right: -10px;
    top: 4px;
  }

  .more {
    width: 24px;
    height: 24px;
    cursor: pointer;
    background-image: url("~@/assets/originSvg/notebook/more.svg?url");
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: center;
  }

  :global(.acud-loading-loading-wrapper) {
    height: calc(100% - 92px);
    overflow-y: auto;
  }
}
