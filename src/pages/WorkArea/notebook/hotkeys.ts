import {HotkeyConfig, createHotkey, isMac} from '@hooks/useHotkeys';

// 笔记本相关的快捷键上下文
export enum NotebookHotkeyContext {
  NOTEBOOK = 'notebook',
  EDITOR = 'notebook-editor',
  TOOLBAR = 'notebook-toolbar'
}

const createSaveHotkey = (handler: (e: KeyboardEvent) => void): HotkeyConfig => {
  const key = 'S';
  return createHotkey(key, handler, {
    description: '保存',
    metaKey: isMac, // Mac 使用 Command 键
    ctrlKey: !isMac // 非 Mac 使用 Ctrl 键
  });
};

// 笔记本工具栏的快捷键配置
export const createToolbarHotkeys = (actions: {save: () => void}): HotkeyConfig[] => {
  return [
    // 保存快捷键
    createSaveHotkey(() => actions.save())
    // 可以添加更多工具栏相关的快捷键
  ];
};

export const hotkeyMap = new Map<string, HotkeyConfig>();
hotkeyMap.set(
  'save',
  createSaveHotkey(() => {})
);

// 单元格相关快捷键在jupyter-notebook中注册，这里只增加快捷键描述
hotkeyMap.set('run', {
  key: 'Enter',
  description: '运行单元格',
  ctrlKey: true
} as HotkeyConfig);
hotkeyMap.set('delete', {key: 'D,D', description: '删除单元格'} as HotkeyConfig);
hotkeyMap.set('copy', {key: 'C', description: '复制单元格'} as HotkeyConfig);
hotkeyMap.set('paste', {key: 'V', description: '粘贴单元格'} as HotkeyConfig);
hotkeyMap.set('cut', {key: 'X', description: '剪切单元格'} as HotkeyConfig);
hotkeyMap.set('insertAbove', {key: 'A', description: '在上方插入单元格'} as HotkeyConfig);
hotkeyMap.set('insertBelow', {key: 'B', description: '在下方插入单元格'} as HotkeyConfig);
